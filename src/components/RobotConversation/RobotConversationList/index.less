.robotConversationListContainer {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 0 20px 16px;
  background-color: var(--primary-background-color-17);

  .robotConversationListHeader {
    // height: 40px;
    flex-shrink: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0 24px;
    .robotConversationListTitle {
      display: flex;
      align-items: center;
      font-size: 18px;
      font-weight: 600;
      line-height: 28px;
      color: var(--primary-text-color-1);
    }
    .closeIcon {
      width: 28px;
      height: 28px;
      cursor: pointer;
      img {
        width: 100%;
        height: 100%;
      }
    }
  }
  .createConversation {
    flex-shrink: 0;
    width: 100%;
    height: 48px;
    display: flex;
    align-items: center;
    color: var(--primary-text-color-9);
    border: 1px solid var(--primary-border-color);
    border-radius: 8px;
    background: var(--primary-background-color-6);
    padding: 0 12px;
    cursor: pointer;
    > img {
      width: 20px;
      height: 20px;
      margin-right: 10px;
    }
  }
  .line {
    flex-shrink: 0;
    width: 100%;
    height: 1px;
    background: var(--primary-background-color-5);
    margin: 20px 0;
  }
  .historyTitle {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: 600;
    color: var(--primary-text-color-1);
    line-height: 22px;
    margin-bottom: 8px;
    > img {
      width: 20px;
      height: 20px;
      margin-right: 10px;
    }
  }
  .historyList {
    height: 100%;
    overflow: auto;
    &::-webkit-scrollbar {
      display: none;
    }
    .date {
      font-size: 15px;
      color: var(--primary-text-color-7);
      line-height: 22px;
      margin: 12px 0;
    }
    .conversationItem {
      padding: 12px;
      background: var(--primary-background-color-6);
      border-radius: 8px;
      margin-bottom: 8px;
      position: relative;
      cursor: pointer;
      font-family: PingFangSC-Regular, "lato", EmojiMart;
      .headerBox {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .title {
          flex: 1 1;
          font-size: 15px;
          font-weight: 600;
          color: var(--primary-text-color-1);
          line-height: 22px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .dateBox {
          display: flex;
          font-size: 13px;
          line-height: 18px;
          color: var(--primary-text-color-7);
        }
      }
      .more {
        display: none;
        width: 28px;
        height: 28px;
        justify-content: center;
        align-items: center;
        border-radius: 6px;
        position: absolute;
        right: 6px;
        top: 8px;
        > img {
          width: 20px;
          height: 20px;
        }
        &:hover {
          background: var(--primary-background-color-15);
        }
      }
      .content {
        font-size: 13px;
        line-height: 20px;
        color: var(--primary-text-color-3);
        word-break: break-all;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 3; /* 控制显示的行数 */
        overflow: hidden;
        text-overflow: ellipsis;
      }
      &:hover {
        background: var(--primary-background-color-15);
        .headerBox {
          .dateBox {
            display: none;
          }
        }
        .more {
          display: flex;
        }
      }
    }
    .active {
      background: var(--tab-actived-background-color) !important;
    }
  }
  .emptyWarp {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 15px;
    color: var(--primary-text-color-7);
  }
  :global {
    .linkflow-spin-nested-loading {
      flex: 1 1;
      overflow: hidden;
      .linkflow-spin-container {
        height: 100%;
      }
    }
  }
}

.menuWrap {
  padding-top: 0;
  padding-bottom: 0;

  .menuContent {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 140px;
    height: 48px;
    border-radius: 8px;
    border: 1px solid var(--primary-border-color);
    background-color: var(--primary-background-color-6);
    .menuItem {
      display: flex;
      align-items: center;
      width: 128px;
      height: 36px;
      font-size: 14px;
      color: var(--primary-text-color-1);
      line-height: 20px;
      border-radius: 6px;
      padding: 0 18px;
      cursor: pointer;
      &:hover {
        background: var(--primary-background-color-15);
      }
    }
  }

  :global {
    .linkflow-popover-arrow {
      display: none;
    }

    .linkflow-popover-inner-content {
      padding: 0;
    }

    .linkflow-popover-content {
      padding-bottom: 0;
      padding-top: 0;
    }

    .linkflow-popover-inner {
      background-color: transparent;
      box-shadow: none;
    }
  }
}
