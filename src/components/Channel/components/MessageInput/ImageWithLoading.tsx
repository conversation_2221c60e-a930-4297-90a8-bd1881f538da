import { FC, useEffect, useState, useRef } from 'react';
import styles from './index.less';

interface ImageWithLoadingProps {
  loadingSrc: string;
  hoverSrc?: string;
  src: string;
}

const ImageWithLoading: FC<ImageWithLoadingProps> = ({
  src,
  hoverSrc,
  loadingSrc,
  ...rest
}) => {
  const [finalSrc, setFinalSrc] = useState(loadingSrc);
  const [loaded, setLoaded] = useState(false);
  const currentSrcRef = useRef(src); // 记录当前目标 src

  const loadImage = (targetSrc: string) => {
    setLoaded(false);
    setFinalSrc(loadingSrc);
    currentSrcRef.current = targetSrc;

    const img = new Image();
    img.src = targetSrc;
    img.onload = () => {
      // 只在当前目标是 targetSrc 时才更新，避免异步竞态
      if (currentSrcRef.current === targetSrc) {
        setLoaded(true);
        setFinalSrc(targetSrc);
      }
    };
    img.onerror = () => {
      // 出错就回退到 loadingSrc
      if (currentSrcRef.current === targetSrc) {
        setLoaded(true);
        setFinalSrc(loadingSrc);
      }
    };
  };

  // 初始加载 src
  useEffect(() => {
    loadImage(src);
  }, [src]);

  return (
    <img
      src={finalSrc}
      onMouseEnter={() => {
        if (hoverSrc) {
          loadImage(hoverSrc);
        }
      }}
      onMouseLeave={() => {
        loadImage(src);
      }}
      className={!loaded ? styles.imgLoading : ''}
      {...rest}
    />
  );
};

export default ImageWithLoading;
