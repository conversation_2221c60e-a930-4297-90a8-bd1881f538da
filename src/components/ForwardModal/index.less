.forwardModalWrap {
  display: flex;
  flex-direction: row;
  height: 100%;

  .left {
    width: 380px;
    height: 100%;
    background: #fafafa;
    display: flex;
    flex-direction: column;
    border-radius: 8px;

    .search {
      margin: 30px 30px 4px;

      :global {
        .linkflow-select-selector {
          background: var(--link-color-base-pry);
          border-radius: 8px;
          border: 1px solid var(--link-color-otl-sec);
          height: 36px !important;

          .linkflow-select-selection-placeholder {
            color: var(--link-color-content-sec);
          }
        }
      }
    }

    .result {
      flex: 1;
      overflow-y: overlay;
      overflow-x: hidden;
    }

    .searchInputContent {
      width: 320px;
      height: 38px;
      background: #ffffff;
      border-radius: 8px;
      border: 1px solid #c6c8ca;
      display: flex;
      justify-content: center;
      align-items: center;

      :global {
        .linkflow-input {
          padding: 0;
          border: none;
          height: 100%;
        }
      }

      .iconContainer {
        margin-left: 16px;
        margin-right: 8px;
        display: flex;
        align-items: center;

        > img {
          width: 18px;
          height: 18px;
        }
      }

      .inputWrapper {
        flex: 1;
        position: relative;
        display: flex;
        align-items: center;
        overflow: hidden;

        .inputDiv {
          flex: 1;

          &:focus-visible {
            outline: none;
          }
        }

        .placeholderWrapper {
          position: absolute;
          left: 0;
          top: 0;
          font-size: 15px;
          text-overflow: ellipsis;
          white-space: nowrap;
          color: rgba(29, 28, 29, 100%);
          opacity: 0.5;
          pointer-events: none;
          font-style: normal;
        }
      }

      .deleteIcon {
        width: 50px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    .scrollBox {
      max-height: 300px;
      overflow-y: auto;
      overflow-x: hidden;
      background-color: red;
      margin: 8px 0;
    }

    .listwrapper {
      padding-bottom: 8px;

      .listItem {
        display: flex;
        align-items: center;
        min-height: 40px;
        padding: 4px 28px 4px 20px;
        background-color: var(--link-color-base-pry);
        color: var(--link-color-content-pry);
        cursor: pointer;

        .showName {
          margin-left: 12px;
          font-size: 16px;
          font-weight: bold;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .searchIcon {
          width: 18px;
          height: 18px;
        }

        .blueBox {
          margin-left: 5px;
          padding: 0 4px;
        }

        .listSearchIcon {
          display: block;
        }

        .listSearchWhiteIcon {
          display: none;
        }

        &:hover {
          background-color: var(--link-color-theme-base-pry);
          color: var(--link-color-base-pry);

          .listSearchIcon {
            display: none;
          }

          .listSearchWhiteIcon {
            display: block;
          }

          .blueBox {
            color: var(--link-color-base-pry);
          }
        }
      }
    }

    .inputWarp {
      margin-bottom: 16px;
      border-radius: 8px;
      border: 1px solid var(--link-color-otl-sec);
      overflow: hidden;
    }

    .footer {
      display: flex;
      flex-direction: row-reverse;

      .footerBtn {
        width: 80px;
        height: 36px;
        background: var(--file-backgroud-color);
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
        font-weight: 600;
        line-height: 22px;
        color: var(--primary-text-color-3);
        cursor: pointer;
      }

      .active {
        color: var(--link-color-base-inv-hgl-1);
      }
    }
  }

  .right {
    flex: 1;
    height: 100%;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    background-color: #fff;
    margin: 0 0 0 30px;

    .forwardToArea {
      width: 100%;
      flex: 1;
      overflow-x: hidden;
      overflow-y: auto;
      display: flex;
      flex-direction: column;
    }

    .toTitle {
      display: flex;
      align-items: center;
      margin: 35px 30px 0 0;
    }

    .header {
      font-size: 18px;
      font-weight: 600;
      color: #2f3035;
      margin: 0 30px 0 0;
    }

    .totalCount {
      margin-left: 8px;
      font-size: 14px;
      font-weight: 400;
      color: #666771;
      flex: 1;
    }

    .closeIcon {
      cursor: pointer;
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;

      img {
        width: 28px;
        height: 28px;
      }

      &:hover {
        background: rgba(107, 107, 108, 8%);
        border-radius: 6px;
      }
    }

    .content {
      overflow-x: hidden;
      // overflow-y: auto;
      overflow-y: overlay;
      flex: 1;
    }

    .line {
      height: 1px;
      background: #dddee0;
      margin-top: 5px;
      margin-bottom: 14px;
      margin-right: 30px;
    }

    .msgWrapper {
      // cursor: pointer;
      margin-right: 30px;
    }

    .footer {
      margin: 14px 0 29px;
      display: flex;

      .footerBtn {
        width: 154px;
        height: 36px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        cursor: pointer;

        &.cancel {
          background: #dddee0;
          color: #1d1c1d;
        }

        &.confirm {
          margin-left: 12px;
          background: #0074e2;
          color: #ffffff;

          &.disable {
            opacity: 0.5;
            cursor: not-allowed;
          }
        }
      }
    }
  }
}

.selectContainer {
  padding: 0;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 0 0 1px var(--link-color-theme-surf-ter-hover),
    0 5px 10px #0000001f;
  border: none;

  :global {
    .linkflow-select-item-empty {
      background-color: rgba(var(--link-color-plt-gray-0), 1);
      border-radius: 8px;
      border: 1px solid rgba(var(--link-color-plt-gray-100), 0.13);
      font-size: 15px;
      color: var(--link-color-content-pry);
      padding: 12px 24px;
    }

    .rc-virtual-list-holder {
      overflow-y: auto;
      max-height: 224px;
      background-color: rgba(var(--link-color-plt-gray-0), 1);
      border-radius: 8px;
      border: 1px solid rgba(var(--link-color-plt-gray-100), 0.13);
    }

    .linkflow-select-item {
      padding: 0;

      &:first-of-type {
        margin-top: 12px;
      }

      &:last-of-type {
        margin-bottom: 12px;
      }

      &:hover {
        .spriteicon {
          color: var(--primary-text-color-pressed);
        }
      }
    }

    .linkflow-select-item-option-content {
      white-space: normal;
    }

    .linkflow-select-item-option-state {
      position: absolute;
      left: 5px;
      top: 5px;

      .spriteicon {
        font-size: 12px;
      }
    }
  }

  .option {
    .memberInfo {
      padding: 6px 24px;
      display: flex;
      align-items: center;
      font-size: 14px;
      font-weight: 600;
      color: var(--link-color-content-pry);
      line-height: 20px;
      word-break: break-all;
      cursor: pointer;

      .showName {
        margin-left: 8px;
        display: flex;
        align-items: center;
      }

      &:hover {
        background-color: #1264a3;
        color: var(--primary-text-color-pressed);
      }
    }

    .groupName {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      display: block;
    }
  }
}

.forwardModalWrapper {
  :global {
    .linkflow-modal-content {
      border-radius: 8px;
    }

    .linkflow-select-selection-item {
      padding: 0;
      background: rgba(29, 155, 209, 10%);
      border-radius: 4px;
      height: 26px;
      margin: 0 8px 0 0;
    }

    .ProseMirror {
      max-width: 440px;
      max-height: 200px;
      overflow: auto;
    }
  }

  .labelOption {
    height: 26px;
    margin-left: 4px;
    background-color: #1d9bd11a;

    .memberInfo {
      height: 26px;
      display: flex;
      align-items: center;

      .showName {
        font-size: 15px;
        font-weight: 600;
        color: var(--link-color-content-pry);
        line-height: 22px;
        margin-left: 6px;
      }

      .groupName {
        font-size: 15px;
        font-weight: 600;
        color: var(--link-color-content-pry);
        line-height: 22px;
        margin-left: 4px;
        white-space: nowrap;
        max-width: 300px;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .closeIcon {
        width: 22px;
        height: 22px;
        margin: 0 2px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;

        > img {
          width: 16px;
        }

        &:hover {
          background: rgba(0, 0, 0, 5%);
          border-radius: 4px;
        }
      }
    }
  }
}

.fileMessageRenderContent {
  display: flex;
  // width: 424.4px;
  width: 100%;
  height: 65px;
  padding: 0 12px;
  border-radius: 10px;
  border: 1px solid var(--offline-border-color);
  align-items: center;
  // cursor: pointer;
  position: relative;
  background-color: var(--primary-background-color-6);

  .icon {
    flex-shrink: 0;
    width: 41px;
    height: 41px;
    border-radius: 8px;
    overflow: hidden;
    margin-right: 12px;
    display: flex;
    align-items: center;
    justify-content: center;

    > img {
      width: 40px;
      height: 40px;
    }
  }

  .fileInfo {
    flex: 1 1;
    display: flex;
    flex-direction: column;
    font-size: 12px;
    font-weight: 400;
    color: var(--primary-text-color);
    line-height: 17px;
    overflow: hidden;

    .fileName {
      width: 100%;
      font-size: 15px;
      font-weight: 600;
      font-family: PingFangSC-Regular;
      color: var(--primary-text-color-1);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin-bottom: 2px;
    }

    .info {
      height: 17px;
    }
  }
}

.pictureMessageRenderContent {
  display: flex;
  // width: 424.4px;
  width: 100%;
  height: 65px;
  padding: 0 12px;
  border-radius: 10px;
  border: 1px solid var(--offline-border-color);
  align-items: center;
  // cursor: pointer;
  position: relative;
  background-color: var(--primary-background-color-6);

  .icon {
    flex-shrink: 0;
    width: 41px;
    height: 41px;
    border-radius: 8px;
    overflow: hidden;
    margin-right: 12px;
    display: flex;
    align-items: center;
    justify-content: center;

    > img {
      max-width: 40px;
      max-height: 40px;
    }
  }

  .fileInfo {
    flex: 1 1;
    display: flex;
    flex-direction: column;
    font-size: 12px;
    font-weight: 400;
    color: var(--primary-text-color);
    line-height: 17px;
    overflow: hidden;

    .fileName {
      width: 100%;
      font-size: 15px;
      font-weight: 600;
      font-family: PingFangSC-Regular;
      color: var(--primary-text-color-1);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      height: 20px;
    }

    .info {
      height: 17px;
    }
  }
}

.textMessageRenderContent {
  cursor: pointer;
  display: flex;
  align-items: center;

  .textArea {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: 1;
    font-family: lato, EmojiMart;
  }

  img {
    width: 12px;
    height: 12px;
    margin-left: 5px;
    cursor: pointer;
  }
}

.popStateContainer {
  .textContent {
    max-width: 450px;
    max-height: 460px;
    overflow-x: hidden;
    overflow-y: scroll;
  }

  :global {
    .linkflow-popover-inner-content {
      padding: 12px 0 12px 16px;
    }
  }
}

.faceMessageRenderContent {
  margin: 12px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  img {
    max-width: 120px;
    max-height: 120px;
  }
}
